from datetime import datetime
from typing import List, Dict
from sqlmodel import SQLModel
from pydantic import field_validator
from .utils import parse_date_util
from .project import ProjectResponse


class ForecastRequest(SQLModel):
    """Request model for generating forecasts."""

    start_date: datetime
    end_date: datetime

    @field_validator("start_date", "end_date", mode="before")
    def validate_date(cls, v):
        """Validate that the date string is in the correct format."""
        return parse_date_util(v)

    @field_validator("end_date")
    def validate_end_date(cls, v, info):
        """Validate that end_date is after start_date."""
        if info.data and "start_date" in info.data:
            start = info.data["start_date"]
            if v <= start:
                raise ValueError("End date must be after start date")
        return v


class ForecastResponse(SQLModel):
    """Response model for forecast data."""

    week_dates: List[str]
    baseline_cash: List[float]
    savings_balance: List[float]
    accounts_totals: List[float]
    payroll_expenses_totals: List[float]
    fixed_expenses_totals: List[float]
    misc_expenses_totals: List[float]
    current_projects_expenses: List[float]
    current_projects_purchase_orders: List[float]
    current_projects_invoices: List[float]
    anticipated_projects_expenses: List[float]
    anticipated_projects_purchase_orders: List[float]
    anticipated_projects_invoices: List[float]

    # Project data (consolidated from separate endpoints)
    current_projects: List[ProjectResponse]
    anticipated_projects: Dict[str, List[ProjectResponse]]
